import React, { useState } from 'react'
import { GoogleMap, LoadScript, Autocomplete } from '@react-google-maps/api'
import { InputWithValidation } from '../inputWithValidation/InputWithValidation'

interface AddressAutocompleteProps {
  apiKey: string
  onPlaceSelect: (place: google.maps.places.PlaceResult) => void
}

const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({ apiKey, onPlaceSelect }) => {
  const [autocomplete, setAutocomplete] = useState<google.maps.places.Autocomplete | null>(null)

  const onLoad = (autocomplete: google.maps.places.Autocomplete) => {
    setAutocomplete(autocomplete)
  }

  const onPlaceChanged = () => {
    if (autocomplete !== null) {
      const place = autocomplete.getPlace()
      if (place) {
        onPlaceSelect(place)
      }
    }
  }

  return (
    // @ts-ignore
    // <LoadScript googleMapsApiKey={apiKey} libraries={['places']}>
    <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
      <InputWithValidation labelName="Enter your address" stateName="address" error={false} />
      {/* <input type="text" placeholder="Enter your address" /> */}
    </Autocomplete>
    // </LoadScript>
  )
}

export default AddressAutocomplete
