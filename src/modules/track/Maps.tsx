import { LoadScript, GoogleMap, Polyline, Circle, Info<PERSON>indow, <PERSON><PERSON> } from '@react-google-maps/api'
import { useCallback, useState, Fragment, useRef, useEffect } from 'react'

import { getConfig } from '../../config'
import { Legend, LegendCont, MapCont } from './style'
import { dayjsFormat, getDataFromLocalStorage, uuidToColor } from '../../shared/helpers/util'
import { CheckboxZoneLabel, FlexCol, FlexRow, Loader } from '../../styles/styled'
import { getRecentLocation } from '../../logic/apis/gps'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { GPSObject, StorageKey } from '../../shared/helpers/constants'

export interface PathData {
  name: string
  memberId: string
  timeCards: TimeCard[]
}

export interface TimeCard {
  timeCardId: string
  timeCardName: string
  path: PathType[]
}

export interface PathType {
  lat: number
  lng: number
  activity: number
  memberId: string
  createdAt: string
  timeCardId: string
}

const mapKey = getConfig()?.mapsAPIKey!
const containerStyle = { width: '100%', height: '500px' }

/**
 * Calculates the distance between two geographical coordinates using the Haversine formula.
 *

 * @description
 * This function computes the distance between two points on the Earth's surface
 * given their latitude and longitude using the Haversine formula. This formula
 * accounts for the spherical shape of the Earth and provides an accurate distance
 * measurement in meters. The formula is:
 *
 * d = 2 * R * asin(sqrt(hav(Δφ) + cos(φ1) * cos(φ2) * hav(Δλ)))
 * where:
 * - R is the Earth's radius (mean radius = 6,371,000 meters),
 * - φ1 and φ2 are the latitudes of the two points,
 * - Δφ is the difference in latitudes,
 * - Δλ is the difference in longitudes,
 * - hav is the Haversine function: hav(θ) = sin²(θ/2).
 */
const haversineDistance = (coords1: PathType, coords2: PathType) => {
  const toRad = (x: number) => (x * Math.PI) / 180
  const R = 6371e3 // Radius of the Earth in meters
  const dLat = toRad(coords2.lat - coords1.lat)
  const dLng = toRad(coords2.lng - coords1.lng)
  const lat1 = toRad(coords1.lat)
  const lat2 = toRad(coords2.lat)

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.sin(dLng / 2) * Math.sin(dLng / 2) * Math.cos(lat1) * Math.cos(lat2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c // Distance in meters
}

/**
 * Clusters geographical coordinates that are within a specified distance of each other.
 *
 * @description
 * This function groups geographical coordinates into clusters based on their proximity.
 * If two points are within the specified threshold distance, they are considered to be
 * part of the same cluster. The center of each cluster is calculated as the average
 * position of all points within that cluster. This is useful for visualizing areas
 * with a high density of points by representing them with a single marker or circle.
 */

const THRESHOLD_DISTANCE = 5 // meters
const MIN_RADIUS = 2
const MAX_RADIUS = 10
const RADIUS_MULTIPLIER = 0.2
const lib = ['places']

const clusterCoordinates = (coordinates: PathType[], threshold = THRESHOLD_DISTANCE) => {
  const clusters: any[] = []

  coordinates?.forEach((point) => {
    let addedToCluster = false

    for (let cluster of clusters) {
      if (haversineDistance(cluster.center, point) <= threshold) {
        // Update the center of the cluster
        cluster.center = {
          lat: (cluster.center.lat * cluster.points.length + point.lat) / (cluster.points.length + 1),
          lng: (cluster.center.lng * cluster.points.length + point.lng) / (cluster.points.length + 1),
        }
        cluster.points.push(point)
        addedToCluster = true
        break
      }
    }

    if (!addedToCluster) {
      clusters.push({ center: point, points: [point] })
    }
  })

  return clusters
}

const createCustomIcon = (fillColor: string) => {
  const svgMarker = {
    path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
    fillColor: fillColor,
    fillOpacity: 1,
    strokeWeight: 0,
    rotation: 0,
    scale: 2,
    anchor: new google.maps.Point(12, 22),
  }

  return svgMarker
}

const Maps = ({ data, timeCardIdObj }: { data: PathData[]; timeCardIdObj: { [key: string]: boolean } }) => {
  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [tooltip, setTooltip] = useState<null | PathType>(null)
  const mapRef = useRef(null)

  const [zoomLevel, setZoomLevel] = useState(16)
  const [recentLocation, setRecentLocation] = useState<{ lat: number; long: number; memberId: string } | null>()
  const [recentLoading, setRecentLoading] = useState(false)
  const [recentMemberId, setRecentMemberId] = useState('')
  const [selectedTimeCard, setSelectedTimeCard] = useState('')
  const [visibleTimeCards, setVisibleTimeCards] = useState<Record<string, boolean>>(timeCardIdObj)

  const handlePointMouseOut = useCallback(() => {
    setTooltip(null)
  }, [])

  const names = data?.map((item) => ({
    name: item?.name,
    memberId: item?.memberId,
  }))

  const [selected, setSelected] = useState(names?.[0]?.name)

  const handlePointMouseOver = useCallback((point: PathType) => {
    setTooltip(point)
    setRecentLocation(null)
    setSelected('')
    // setSelected('')
  }, [])

  const selectedMemberData = data?.find((item) => item?.name === selected)
  const selectedTimeCards = selectedMemberData?.timeCards || []
  const selectedCoOrdData = selectedTimeCard
    ? selectedTimeCards?.find((item) => item?.timeCardId === selectedTimeCard)?.path
    : data?.find((itm) => itm?.name === selected)?.timeCards?.[0]?.path

  /**
   * Processes a list of geographical coordinates to produce polyline segments and clusters for circles.
   *
   * @description
   * This function processes the `path` array to separate it into polyline segments and clustered circles.
   * Segments are created between consecutive points where the distance between them exceeds THRESHOLD_DISTANCE meters.
   * Clusters are generated from close points using the `clusterCoordinates` function.
   * This allows rendering of a map with both polylines for longer distances and clusters of circles
   * for points that are close together.
   */
  const getSegmentsAndCircles = (path: any[]) => {
    const segments = []
    const clusters = clusterCoordinates(path)
    // const clusters = path
    const clustersTimeRange = clusterCoordinates(path).map((cluster) => {
      // Check if cluster contains valid points with createdAt
      const validPoints = cluster.points
        ?.filter((point: { createdAt: string }) => point.createdAt)
        ?.sort(
          (a: { createdAt: string }, b: { createdAt: string }) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )

      if (validPoints && validPoints?.length > 0) {
        const startTime = validPoints[0]?.createdAt
        const endTime = validPoints[validPoints?.length - 1]?.createdAt
        return {
          points: cluster.points,
          startTime,
          endTime,
          center: cluster.center,
        }
      }

      // Return the cluster with undefined startTime and endTime if no valid points
      return {
        points: cluster.points,
        startTime: undefined,
        endTime: undefined,
        center: cluster.center,
      }
    })

    if (path?.length >= 1) {
      let isFirst = true
      for (let i = 0; i < path?.length - 1; i++) {
        const point1 = path[i]
        const point2 = path[i + 1]
        const distance = haversineDistance(point1, point2)

        if (distance > THRESHOLD_DISTANCE) {
          isFirst = false
          segments.push({
            path: [point1, point2],
            key: `segment-${i}`,
          })
        }
      }
      if (isFirst) {
        const point1 = path[0]
        // const point2 = path[0 + 1]
        segments.push({
          path: [point1, point1],
          key: `segment-${0}`,
        })
      }
    }

    return { segments, circles: clusters, clustersTimeRange }
  }

  const getRadius = (point: number[]) => {
    const value = MIN_RADIUS + (point?.length - 1) * RADIUS_MULTIPLIER
    return value > MAX_RADIUS ? MAX_RADIUS : value
  }

  const onLoad = useCallback((map: any) => {
    mapRef.current = map
  }, [])

  const fitBounds = useCallback(() => {
    if (!mapRef.current) return

    const bounds = new window.google.maps.LatLngBounds()

    // Extend bounds with polylines
    data?.forEach((user) => {
      const { segments } = getSegmentsAndCircles(user?.timeCards?.[0]?.path)
      segments.forEach((segment) => {
        segment?.path?.forEach((point) => {
          bounds.extend(point)
        })
      })
    })

    // @ts-ignore
    mapRef.current.fitBounds(bounds)
  }, [data])

  // Adjust bounds on initial render and when data changes
  useEffect(() => {
    fitBounds()
  }, [data, fitBounds])

  const handleLiveLocation = async (memberId: string) => {
    try {
      setRecentLoading(true)
      setRecentMemberId(memberId)
      const response = await getRecentLocation({
        memberId,
      })

      const data = response?.data?.data?.recentLocation

      setRecentLocation(data)
      setZoomLevel(20)
    } catch (error) {
    } finally {
      setRecentLoading(false)
      setRecentMemberId('')
    }
  }

  return (
    <MapCont>
      {/* <LoadScript
        googleMapsApiKey={mapKey}
        // @ts-ignore
        libraries={lib}
      > */}
      <GoogleMap
        mapContainerStyle={containerStyle}
        center={
          recentLocation?.lat
            ? { lat: Number(recentLocation?.lat), lng: Number(recentLocation?.long) }
            : selectedCoOrdData?.length
            ? {
                lat: selectedCoOrdData?.[0]?.lat,
                lng: selectedCoOrdData?.[0]?.lng,
              }
            : undefined
        }
        options={{
          disableDefaultUI: true,
          zoomControl: true,
          mapTypeId: 'terrain',
          mapTypeControl: false,
          fullscreenControl: true,
        }}
        zoom={zoomLevel}
        onLoad={onLoad}
        id="maps"
      >
        {recentLocation?.memberId && (
          <Marker
            position={{
              lat: Number(recentLocation?.lat),
              lng: Number(recentLocation?.long),
            }}
            icon={createCustomIcon(uuidToColor(recentLocation.memberId))}
          />
        )}
        {data?.map((member) => (
          <Fragment key={member.name}>
            {member.timeCards?.map((timeCard) => {
              const { segments, circles, clustersTimeRange } = getSegmentsAndCircles(timeCard.path)

              return (
                <Fragment key={`${member.memberId}-${timeCard.timeCardId}`}>
                  {/* Render segments */}
                  {segments.map((segment) => (
                    <Fragment key={segment?.key}>
                      {segment?.path?.map((point: PathType, index) => (
                        <>
                          {visibleTimeCards[timeCard.timeCardId] ? (
                            <Fragment key={index}>
                              <Polyline
                                path={[
                                  {
                                    lat: point?.lat,
                                    lng: point?.lng,
                                  },
                                ]}
                                options={{
                                  strokeOpacity: 1,
                                  editable: true,
                                  strokeColor: uuidToColor(point?.memberId),
                                  strokeWeight: 2,
                                }}
                                onMouseOver={() => handlePointMouseOver(point)}
                                onMouseOut={handlePointMouseOut}
                              />
                              {tooltip?.createdAt === point?.createdAt && point?.memberId === tooltip?.memberId && (
                                <InfoWindow
                                  options={{ headerContent: member?.name, disableAutoPan: true }}
                                  position={{
                                    lat: tooltip?.lat,
                                    lng: tooltip?.lng,
                                  }}
                                  // onCloseClick={() => setTooltip(null)}
                                >
                                  <div onMouseOver={() => handlePointMouseOver(point)} onMouseOut={handlePointMouseOut}>
                                    <div className="divider"></div>
                                    {clustersTimeRange.map((cluster, clusterIndex) => {
                                      const isPointInCluster = cluster.points.some(
                                        (clusterPoint: PathType) =>
                                          clusterPoint.lat === point.lat && clusterPoint.lng === point.lng
                                      )

                                      if (isPointInCluster) {
                                        return (
                                          <Fragment key={clusterIndex}>
                                            {dayjsFormat(cluster.startTime, 'hh:mm A') ===
                                            dayjsFormat(cluster.endTime, 'hh:mm A') ? (
                                              <p>{dayjsFormat(cluster.startTime, 'hh:mm A')}</p>
                                            ) : (
                                              <p>
                                                {dayjsFormat(cluster.startTime, 'hh:mm A')} -{' '}
                                                {dayjsFormat(cluster.endTime, 'hh:mm A')}
                                              </p>
                                            )}
                                          </Fragment>
                                        )
                                      }
                                      return null
                                    })}

                                    <p>lat: {point?.lat}</p>
                                    <p>lng: {point?.lng}</p>
                                    <p>Activity: {GPSObject[point?.activity]}</p>
                                  </div>
                                </InfoWindow>
                              )}

                              <Polyline
                                path={segment?.path?.map((point: PathType) => ({
                                  lat: point?.lat,
                                  lng: point?.lng,
                                }))}
                                options={{
                                  strokeColor: uuidToColor(point?.memberId),
                                  strokeOpacity: 1.0,
                                  // editable: true,
                                  strokeWeight: 2,
                                }}
                              />
                            </Fragment>
                          ) : (
                            <></>
                          )}
                        </>
                      ))}
                    </Fragment>
                  ))}

                  {/* Render circles */}
                  {circles.map((cluster, index) => (
                    <Fragment key={`circle-${index}`}>
                      {visibleTimeCards?.[cluster?.points?.[0]?.timeCardId] ? (
                        <Circle
                          key={`circle-${index}`}
                          center={cluster.center}
                          radius={getRadius(cluster?.points)} // Radius based on cluster size
                          options={{
                            fillColor: uuidToColor(cluster?.points?.[0]?.memberId),
                            fillOpacity: 0.6,
                            strokeColor: uuidToColor(cluster?.points?.[0]?.memberId),
                            strokeOpacity: 1.0,
                            strokeWeight: 1,
                          }}
                        />
                      ) : (
                        <></>
                      )}
                    </Fragment>
                  ))}
                </Fragment>
              )
            })}
          </Fragment>
        ))}
      </GoogleMap>
      {/* </LoadScript> */}

      <FlexCol gap="16px">
        {data?.map((member, idx) => (
          <FlexCol key={idx}>
            <FlexRow className="legend" width="max-content">
              <LegendCont>
                {recentLoading && member?.memberId === recentMemberId && (
                  <Loader id="load" color={uuidToColor(member?.memberId)} width="25px" height="25px" />
                )}
                <Legend
                  bg={uuidToColor(member.memberId)}
                  onClick={() => {
                    handleLiveLocation(member.memberId)
                  }}
                />
              </LegendCont>
              <CheckboxZoneLabel>
                <span
                  onClick={() => {
                    if (selected === member.name) {
                      setSelected('')
                      return
                    }
                    setSelected(member.name)
                    // setRecentLocation(null)
                    setZoomLevel(18)
                  }}
                >
                  {member.name}
                </span>
              </CheckboxZoneLabel>
            </FlexRow>

            {/* Show timecard details if this member is selected */}
            {selected === member.name && (
              <FlexCol style={{ marginLeft: '20px' }}>
                {member.timeCards?.map((timeCard, tcIdx) => (
                  <FlexRow key={tcIdx} width="max-content" gap="4px">
                    <LegendCont>
                      {/* <Legend
                        className="no-animation"
                        bg={uuidToColor(member.memberId)}
                        style={{ width: '10px', height: '10px' }}
                      /> */}
                      <input
                        type="checkbox"
                        defaultChecked={timeCardIdObj[timeCard.timeCardId]}
                        onChange={(e) => {
                          const isVisible = e.target.checked
                          setVisibleTimeCards((prev) => ({
                            ...prev,
                            [timeCard.timeCardId]: isVisible,
                          }))
                        }}
                        style={{ marginLeft: '4px' }}
                      />
                    </LegendCont>
                    <CheckboxZoneLabel>
                      <span
                        style={{ fontSize: '0.9em' }}
                        onClick={() => {
                          setSelectedTimeCard(timeCard.timeCardId)
                          setZoomLevel(20)
                          setRecentLocation(null)
                          setSelected(member.name)
                        }}
                      >
                        {timeCard.timeCardName}
                      </span>
                    </CheckboxZoneLabel>
                  </FlexRow>
                ))}
              </FlexCol>
            )}
          </FlexCol>
        ))}
      </FlexCol>
    </MapCont>
  )
}

export default Maps
