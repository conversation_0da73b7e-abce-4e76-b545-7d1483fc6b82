# Page Break Implementation for FormPDFLayout

## Overview
This document describes the page break functionality implemented in the FormPDFLayout component to prevent content from being broken across pages when generating PDFs.

## Features

### 1. Intelligent Page Break Logic
The `shouldAddPageBreak` function determines when to add page breaks based on:

- **Large File Content**: Adds page breaks before file fields with more than 3 files
- **Location Fields with Images**: Adds page breaks before location fields that contain images
- **Major Section Headers**: Adds page breaks before h1/h2 headers when there's substantial content before them
- **Content Density**: Adds page breaks before headers when there's been significant recent content

### 2. CSS Classes for Page Control

#### Page Break Classes
- `.page-break-before`: Forces a page break before the element
- `.page-break-after`: Forces a page break after the element
- `.avoid-break`: Prevents breaking inside the element
- `.no-break`: Prevents breaking inside the element (existing class)

#### Field-Specific Classes
- `.field-group`: Prevents breaking inside field groups
- `.field-group-col`: Prevents breaking inside column field groups
- `.section-title`: Prevents breaking inside section titles

### 3. Print-Specific Styles
The CSS includes `@media print` rules that:
- Remove borders and adjust layout for printing
- Enforce page break rules more strictly
- Control orphan and widow lines
- Ensure images and file groups stay together

## Usage

### Automatic Page Breaks
The component automatically applies page breaks based on content analysis:

```tsx
const needsPageBreak = shouldAddPageBreak(field, index, formattedResponses)
```

### Manual Page Break Control
You can manually control page breaks by adding CSS classes:

```tsx
<div className="page-break-before">
  {/* Content that should start on a new page */}
</div>

<div className="no-break">
  {/* Content that should not be split across pages */}
</div>
```

## Implementation Details

### Page Break Decision Logic

1. **First Field Exception**: Never adds page breaks for the first field
2. **File Content Threshold**: Breaks before file fields with 4+ files
3. **Location Images**: Breaks before location fields with images
4. **Header Analysis**: 
   - Major headers (h1, h2) with 3+ content fields before them
   - Any header with 3+ recent content fields in the last 5 fields
5. **Content Filtering**: Only counts meaningful content (excludes headers, paragraphs, and empty values)

### CSS Print Optimization

```css
@media print {
  .inspection-report {
    width: 100%;
    height: auto;
    border: none;
    padding: 0.5in;
    margin: 0;
  }
  
  .avoid-break,
  .no-break,
  .field-group,
  .field-group-col {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
```

## Testing

The implementation includes comprehensive tests that verify:
- Page break classes are applied correctly for large file content
- Location fields with images get page breaks
- Major headers with sufficient content get page breaks
- First fields never get page breaks
- All field types render correctly

## Browser Compatibility

The page break implementation uses both modern CSS properties and legacy fallbacks:
- `break-inside: avoid` (modern)
- `page-break-inside: avoid` (legacy)
- `break-before: page` (modern)
- `page-break-before: always` (legacy)

## PDF Generation Integration

The page break functionality works with the html2pdf.js library configuration:

```javascript
html2pdf().set({
  pagebreak: { mode: ['avoid', 'css'] },
  // ... other options
})
```

This ensures that CSS page break rules are respected during PDF generation.

## Customization

To customize page break behavior:

1. **Modify the `shouldAddPageBreak` function** to change when page breaks are applied
2. **Adjust CSS classes** to change how page breaks are styled
3. **Update thresholds** for file count, content count, etc.

## Best Practices

1. **Test with Real Content**: Always test page breaks with realistic form content
2. **Consider Content Length**: Very long text fields may still break across pages
3. **Image Optimization**: Large images may force page breaks regardless of CSS
4. **Print Preview**: Always use browser print preview to verify page breaks
5. **Mobile Considerations**: Page breaks primarily affect print/PDF, not mobile display
