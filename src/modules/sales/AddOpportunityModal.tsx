import { Field, Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'
import { getLeadSources } from '../../logic/apis/leadSource'
import { getPosition } from '../../logic/apis/position'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import {
  dayjsFormat,
  daysSince,
  formatPhoneNumber,
  getBooleanFromName,
  getIdFromName,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
} from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { createOpportunity, getStages } from '../../logic/apis/sales'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { I_Stage } from '../opportunity/components/assessmentForm/AssessmentForm'
import { getProjectTypes } from '../../logic/apis/projects'
import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'
import { CrossContainer, ModalContainer, ModalHeader, ModalHeaderContainer, ModalHeaderInfo } from './style'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import Button from '../../shared/components/button/Button'
import { getSalesPersonAndPM } from '../../logic/apis/company'
import { Nue, StageGroupEnum } from '../../shared/helpers/constants'
import { getDistanceAndDuration, getLatLongFromAddress } from '../../shared/helpers/map'
import { SLoader } from '../../shared/components/loader/Loader'
import useDebounce from '../../shared/hooks/useDebounce'
import { getDepartments } from '../../logic/apis/department'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import { getFormattedLeadSrcData, getLeadSrcDropdownId, getLeadSrcDropdownName } from '../leadSource/LeadSource'
import AutoCompleteIndentation from '../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import {
  getContactById,
  getContactOpportunityById,
  getLinkedContact,
  getSearchedContact,
} from '../../logic/apis/contact'
import SearchableDropdown from '../../shared/searchableDropdown/SearchableDropdown'
import { fetchSearchReferer } from '../contact/components/contactProfile/ContactProfile'
import { CustomModal } from '../../shared/customModal/CustomModal'
import FoundOpportunity from './FoundOpportunity'
import InfoOpportunityModal from './InfoOpportunityModal'
import { useParams } from 'react-router-dom'
import AutoCompleteWithId from '../../shared/autoComplete/AutoCompleteWithId'
import { mergeSourceAndCampaignNames } from '../contact/components/addNewContactModal/AddNewContactModal'

export interface I_LeadSource {
  _id: string
  name: string
  channelId: string
  deleted: boolean
  channelName: string[]
}

export interface I_SalesPerson {
  _id: string
  email: string
  name: string
  // username: string
  company: string
  user: string
  invited: boolean
  roleId: string
  deleted: boolean
}

export interface I_Position {
  _id: string
  position: string
  permissions: any
  deleted: boolean
  createdBy: string
}

export interface I_Client {
  _id: string
  isBusiness: boolean
  name: string
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: number
  phone: number
  leadSourceName: string
  businessName: string
  notes: string
  createdBy: string
  deleted: boolean
}

interface ICategoryModal {
  onClose: () => void
  onComplete: () => void
  setReferrerValue: any
  refererres: any
  clientAutoFill?: any
  setClientData: any
  clientData?: any
  setShowAddNewClientModal?: React.Dispatch<React.SetStateAction<boolean>>
  setShowEditClientModal?: React.Dispatch<React.SetStateAction<boolean>>
  detailsUpdate: any
  setClientName?: any
  createdClient?: any
  setLinkedContacts: React.Dispatch<any>
  linkedContacts?: any
  setShowReferrerModal?: React.Dispatch<React.SetStateAction<boolean>>
  isLead?: boolean
  leadInfo?: any
  activeLead?: any
  setLocalReferrerData?: any
  selectedReferrer?: {
    name: string
    id: string
  }
}
const AddOpportunityModal = (props: ICategoryModal) => {
  const {
    onClose,
    onComplete,
    setReferrerValue,
    refererres,
    clientAutoFill,
    setClientData,
    clientData,
    setShowAddNewClientModal,
    setShowEditClientModal,
    setShowReferrerModal,
    detailsUpdate,
    setClientName,
    createdClient,
    setLinkedContacts,
    linkedContacts,
    isLead,
    leadInfo,
    activeLead,
    setLocalReferrerData,
    selectedReferrer,
  } = props

  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company
  const [toggleGoogleAddressInput, setToggleGoogleAddressInput] = useState<boolean>(false)
  const [addressLoading, setAddressLoading] = useState<boolean>(isLead ? false : true)
  const [projectTypes, setProjectTypes] = useState<any>([])
  const [workType, setWorkType] = useState('')
  const [duration, setDuration] = useState(0)
  const [distance, setDistance] = useState(0)
  const [loading, setLoading] = useState(false)
  const [stages, setStages] = useState<I_Stage[]>([])
  const [clientDropdown, setClientDrop] = useState<I_Client[]>([])
  const [clientloader, setClientLoader] = useState<boolean>(false)
  const [leadSrcData, setLeadSrcData] = useState([])
  const [clientLoading, setClientLoading] = useState(false)
  const [disableContact, setDisableContact] = useState(!!isLead)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [clientAddress, setClientAddress] = useState('')
  const [defaultStage, setDefaultStage] = useState('') // will be stage id
  const [searchValue, setSearchValue] = useState<string>('')
  const [selectedType, setSelectedType] = useState<string>(isLead ? leadInfo?.workTypeName : '')
  const [addressInputType, setAddressInputType] = useState<'custom' | 'google'>()
  const [officeDrop, setOfficeDrop] = useState<any[]>([])
  const [foundOppModal, setFoundOppModal] = useState(false)
  const [infoOpportunityModal, setInfoOpportunityModal] = useState(false)
  const [createNew, setCreateNew] = useState(false)
  const [localReferrerId, setLocalReferrerId] = useState('')
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState<any>()
  const [oppFinalPayload, setOppFinalPayload] = useState<any>(null)
  const [openOppLeads, setOpenOppLeads] = useState<any>(null)
  const { contactId: originalContactId } = useParams()

  const initialvalues = {
    newLeadDate: isLead ? leadInfo?.newLeadDate : '',
    oppDate: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    needsAssessmentDate: '',
    oppType: isLead ? leadInfo?.workTypeName : '',
    oppNotes: '',
    notes: isLead ? leadInfo?.notes : '',
    client: isLead ? leadInfo?.fullName : '',
    contactId: isLead ? leadInfo?.contactId : '',
    street: isLead ? leadInfo?.street : '',
    city: isLead ? leadInfo?.city : '',
    state: isLead ? leadInfo?.state : '',
    zip: isLead ? leadInfo?.zip : '',
    leadSourceName: '',
    CSRAssigned: isLead ? leadInfo?.CSRAssigned : '',
    salesPerson: '',
    referredBy: isLead ? leadInfo?.referredBy : '',
    distance: 0,
    duration: 0,
    leadCost: '0',
    oppLat: '',
    oppLong: '',
    questions: [],
    naDate: '',
    naTime: '',
  }
  console.log({ initialvaluessf: initialvalues }, leadInfo)
  const debouncedSearch = useDebounce(searchValue, 500)

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, StageGroupEnum.Sales)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
          if (stage.code === 'opp') setDefaultStage(stage._id)
        })

        setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const getClientsData = async () => {
    try {
      setClientLoader(true)
      let receivedData: any = []
      const response = await getSearchedContact(debouncedSearch, {
        fields: {
          fullName: 1,
          firstName: 1,
          lastName: 1,
          leadSourceId: 1,
          campaignId: 1,
          isBusiness: 1,
          businessName: 1,
          distance: 1,
          referredBy: 1,
          phone: 1,
          email: 1,
          street: 1,
          city: 1,
          state: 1,
          zip: 1,
        },
      })

      if (isSuccess(response)) {
        let statusRes = response?.data?.data?.contacts
        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            ...res,
            name: `${res?.fullName?.trim()}`,
            status: '-',
            address: res?.street,
            phone: formatPhoneNumber(res?.phone, ''),
            isBusiness: res?.isBusiness,
            clientId: res._id,
          })
        })
        setClientDrop(receivedData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      notify('Failed to fetch clients!', 'error')
      console.log('Contact fetch failed', err)
    } finally {
      setAddressLoading(false)
      setClientLoader(false)
    }
  }
  useEffect(() => {
    // if (!isLead) {
    getClientsData()
    // }
  }, [debouncedSearch])

  useEffect(() => {
    initFetch()
  }, [])

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypes(projectType)
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const isLeadMoreThan2WeeksOld = (leadDate: string): boolean => {
    const leadTime = new Date(leadDate).getTime()
    return daysSince(leadTime) > 14
  }

  const fetchContacLeadOppById = async (contactId: string) => {
    return await getContactOpportunityById(contactId)
  }

  const formatQuestionKey = (question: string) => question?.replace(/\s+/g, '-').replace(/\?/g, '')

  const formatQuestionsData = (questions: any[], values: any): string => {
    return (questions || [])
      .map((q) => {
        const key = formatQuestionKey(q)
        const answer = values[key]?.trim() || ''
        return answer ? `${q} **${answer}**\n` : `${q} \n`
      })
      .join('')
  }

  const handleSubmit = async (values: typeof initialvalues) => {
    const formatDateValidation = (condition: boolean, message: string) => {
      if (condition) {
        notify(message, 'error')
        throw new Error('date error')
      }
    }

    // ---------------------- Date Validations ----------------------
    formatDateValidation(
      values?.needsAssessmentDate && new Date(values.oppDate) > new Date(values.needsAssessmentDate),
      `Appointment date must be after Opportunity date ${dayjsFormat(values.oppDate, 'MMM DD, YYYY @ hh:mm A')}`
    )

    formatDateValidation(
      isLead && new Date(values.oppDate) < new Date(values.newLeadDate),
      `Opportunity date must be after Lead date: ${dayjsFormat(values.newLeadDate, 'MMM DD, YYYY @ hh:mm A')}`
    )

    // ---------------------- Address Geolocation ----------------------
    if (values?.street && values?.city && values?.zip && values?.state) {
      const address = `${values.street}, ${values.city}, ${values.state} ${values.zip}, USA`
      const latLong = await getLatLongFromAddress(address)
      values.oppLat = `${latLong?.lat}` || ''
      values.oppLong = `${latLong?.lng}` || ''
    }

    // ---------------------- Mapping Dropdown IDs ----------------------
    const type = getIdFromName(values.oppType || workType, projectTypesDrop)
    const referredById = localReferrerId || 'unknown'
    const CSRAssignedId = getIdFromName(values.CSRAssigned, officeDrop)
    const result = getLeadSrcDropdownId(values.leadSourceName, leadSrcData)
    const { campaignId, campaignName } = result || {}

    // ---------------------- Format Notes ----------------------
    const formattedData = formatQuestionsData(values?.questions, values)

    values.oppNotes = `${formattedData ? `${formattedData}\n` : ''}Comments/Notes: ${
      values.notes ? `**${values.notes.trim()}**` : ''
    }\n${leadInfo?.notes ? `Contact Notes: **${leadInfo.notes.trim()}**` : ''}`

    // ---------------------- Prepare Submission Object ----------------------
    const { notes, questions, naDate, naTime, contactId, ...restObject }: any = values

    if (questions?.length) {
      const questionKeys = questions.map(formatQuestionKey)
      questionKeys.forEach((key) => delete restObject[key])
    }

    // ---------------------- Extra Parsing ----------------------
    setLoading(true)
    try {
      const isBusiness = getBooleanFromName(values.client, clientDropdown)
      const salesPersonId = salesPersonDrop.find((sp) => sp.name === values.salesPerson)?._id || ''

      values.distance = Number(values.distance || 0)
      values.duration = Number(values.duration || 0)

      const foundClient = clientDropdown?.find((contact: any) => contact.fullName?.trim() === values.client?.trim())
      const contactId = createdClient?._id || foundClient?._id || leadInfo?.contactId

      // Return early (for testing/logging) — remove this in production
      console.log({ createdClient, foundClient, leadInfo })

      const finalPayload = {
        ...restObject,
        contactId: contactId,
        comments: [],
        oppDate: new Date(values.oppDate),
        newLeadDate: values.newLeadDate ? new Date(values.newLeadDate) : new Date(values.oppDate),

        needsAssessmentDate: values?.needsAssessmentDate ? new Date(values.needsAssessmentDate) : undefined,
        companyAddress: companySettingForAll?.address,
        companyLang: companySettingForAll?.longitude,
        companyLat: companySettingForAll?.latitude,
        createdBy: currentMember._id,
        leadSource: result?.leadSourceName,
        originalContact: originalContactId ? originalContactId : undefined,
        // firstName: createdClient?.isBusiness
        //   ? createdClient?.businessName
        //   : createdClient?.firstName || foundClient?.firstName || leadInfo?.firstName || '',
        // lastName:
        //   createdClient?.isBusiness || foundClient?.isBusiness || leadInfo?.isBusiness
        //     ? undefined
        //     : createdClient?.lastName || foundClient?.lastName || leadInfo?.lastName || '',
        csrId: CSRAssignedId,
        oppLat: values.oppLat || undefined,
        oppLong: values.oppLong || undefined,
        leadSourceId: result?.leadSourceId || undefined,
        oppType: type,
        referredBy: referredById || undefined,
        salesPerson: salesPersonId,
        selfGen: result?.leadSourceObject?.code === 'selfGen',
        stage: defaultStage,
        createLead: activeLead?._id ? false : openOppLeads?.leads?._id ? false : true,
        leadId: activeLead?._id ? activeLead?._id : openOppLeads?.leads?._id || undefined,
        currentDate: new Date().toISOString(),
        campaignId: campaignId || undefined,
        campaignName: campaignName || undefined,
      }

      setOppFinalPayload(finalPayload)

      // if (originalContactId === contactId) {
      //   const res = await fetchContacLeadOppById(contactId)
      //   if (isSuccess(res)) {
      //     const { opps = [], leads = {} } = res?.data?.data
      //     setOpenOppLeads(res?.data?.data)
      //     const leadIsRecent = leads?.newLeadDate && isLeadMoreThan2WeeksOld(leads.newLeadDate)
      //     if (leadIsRecent) {
      //       setInfoOpportunityModal(true)
      //       return // wait for modal action
      //     }
      //   }
      // } else {
      const leadIsRecent = openOppLeads?.leads?.newLeadDate && isLeadMoreThan2WeeksOld(openOppLeads?.leads?.newLeadDate)
      if (leadIsRecent) {
        setInfoOpportunityModal(true)
        return // wait for modal action
        //   }
      }

      // Step 2: Check if lead is less than 2 weeks old
      console.log({ firstStep: finalPayload })

      // ---------------------- Final Payload ----------------------
      const response = await createOpportunity(finalPayload)

      setLoading(false)

      if (isSuccess(response)) {
        notify('Created new opportunity!', 'success')
        onComplete()

        setOppFinalPayload(null)
      } else throw new Error(response?.data?.message)
    } catch (err: any) {
      setLoading(false)

      setOppFinalPayload(null)
      notify(err?.message || 'Something went wrong!', 'error')
      console.error('Failed new lead submit', err)
    } finally {
      setLoading(false)
    }
  }

  // Step 3: CreateOpportunity By Action
  const handleCreateOpportunity = async (changedData: any) => {
    try {
      const finalPayload = { ...oppFinalPayload, ...changedData }
      console.log({ oppFinalPayload }, finalPayload)

      const response = await createOpportunity(finalPayload)

      setLoading(false)

      if (isSuccess(response)) {
        notify('Created new opportunity!', 'success')
        onComplete()
      } else throw new Error(response?.data?.message)
    } catch (error) {
      console.log({ error })
    } finally {
      setFoundOppModal(false)
      setLoading(false)
      setOppFinalPayload(null)
      setInfoOpportunityModal(false)
      setOpenOppLeads(null)
    }
  }

  // const handleOpportunityForCreateNew = () => {
  //   if (openOppLeads?.opps?.length) {
  //     setInfoOpportunityModal(false)
  //     setFoundOppModal(true)
  //   } else {
  //     handleCreateOpportunity({ createLead: true })
  //   }
  // }
  console.log({ clientData })

  const fetchClientById = async (clId: string) => {
    setClientLoading(true)
    try {
      // const clientResponse = await getContactById(clId!)
      // const oppResponse = await fetchContacLeadOppById(clId)

      const [clientResponse, oppResponse, linkedResponse] = await Promise.all([
        getContactById(clId!),
        fetchContacLeadOppById(clId!),
        getLinkedContact(clId!),
      ])

      if (isSuccess(clientResponse)) {
        setClientData(clientResponse?.data?.data?.contact)
      }

      if (isSuccess(oppResponse)) {
        const { opps = [], leads = {} } = oppResponse?.data?.data
        setOpenOppLeads(oppResponse?.data?.data)
        if (opps?.length) {
          setFoundOppModal(true)
        }
      }

      if (isSuccess(linkedResponse)) {
        setLinkedContacts(linkedResponse?.data?.data?.linkedContacts || [])
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setClientLoading(false)
    }
  }

  // const fetchClientById = async (clId: string) => {
  //   setClientLoading(true)
  //   try {
  //     // const clientResponse = await getContactById(clId!)
  //     // const oppResponse = await fetchContacLeadOppById(clId)

  //     const [oppResponse, linkedResponse] = await Promise.all([
  //       // getContactById(clId!),
  //       fetchContacLeadOppById(clId!),
  //       getLinkedContact(clId!),
  //     ])

  //     if (clientDropdown?.length) {
  //       const foundClient = clientDropdown?.find((contact: any) => contact._id === clId)
  //       setClientData(foundClient)
  //     }

  //     if (isSuccess(oppResponse)) {
  //       const { opps = [], leads = {} } = oppResponse?.data?.data
  //       setOpenOppLeads(oppResponse?.data?.data)
  //       if (opps?.length) {
  //         setFoundOppModal(true)
  //       }
  //     }

  //     if (isSuccess(linkedResponse)) {
  //       setLinkedContacts(linkedResponse?.data?.data?.linkedContacts)
  //     }
  //   } catch (error) {
  //     console.log({ error })
  //   } finally {
  //     setClientLoading(false)
  //   }
  // }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (isSuccess(leadSourceResponse)) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  const getPositionMembers = async (positionId: string) => {
    try {
      // const response = await getPositionMembersById({ positionId }, false) // NHR-1570
      const response = await getSalesPersonAndPM() // NHR-1570
      if (isSuccess(response)) {
        // setSalesPersonDrop(response?.data?.data?.memberData) // NHR-1570
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position

        let salesPersonIdx: string[] = []
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'SalesPerson') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'GeneralManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'SalesManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'RRTech') {
            salesPersonIdx.push(position?._id)
            return
          }
        })
        getPositionMembers(salesPersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionsCSR = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembersForDepartment(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionMembersForDepartment = async (departmentId: string) => {
    try {
      // const response = await getPositionMembersById({ departmentId }, false) // NHR-1567

      const response = await getSalesPersonAndPM(departmentId) // NHR-1567
      if (isSuccess(response)) {
        // setOfficeDrop(response?.data?.data?.memberData) // NHR-1567
        setOfficeDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const isPayPerLead = (leadSrc: any) => {
    return leadSrc && leadSrc?.channelName === 'defOnlinePayPerLead' // channel
  }

  const getObjectFromKey = (Objs: Array<any>, key: string, value: string) => {
    const [obj] = Objs.filter((obj) => obj[key] === value)
    return obj
  }

  useEffect(() => {
    getLeadSrcData()
    getPositions()
    getStagesData()
    getPositionsCSR()
  }, [detailsUpdate])

  const newLeadSchema = Yup.object().shape({
    oppDate: Yup.string().required('Required'),
    oppType: Yup.string().required('Required'),
    notes: Yup.string(),
    client: Yup.string().required('Required'),
    leadSourceName: Yup.string().required('Required'),
    CSRAssigned: Yup.string().required('Required'),
    duration: Yup.number().when(['street', 'city', 'state', 'zip'], {
      is: (street, city, state, zip) => !street && !city && !state && !zip,
      then: Yup.number().notRequired(),
      otherwise: Yup.number().required('Please enter Duration').min(1, 'Please enter Duration'),
    }),
    salesPerson: Yup.string().required('Required'),
  })

  return (
    <>
      <ModalContainer>
        <ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <ModalHeader>Add Opportunity</ModalHeader>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <CrossContainer
            onClick={() => {
              onClose()
            }}
          >
            <CrossIcon />
          </CrossContainer>
        </ModalHeaderContainer>
        <SharedStyled.SettingModalContentContainer>
          <>
            <Formik
              initialValues={initialvalues}
              onSubmit={handleSubmit}
              validationSchema={newLeadSchema}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={false}
            >
              {({ errors, touched, values, setFieldValue }) => {
                console.log({ errors, touched, values })
                useEffect(() => {
                  if (stages?.length && officeDrop?.length && !isLead) {
                    setFieldValue(
                      'CSRAssigned',
                      getValueByKeyAndMatch('name', stages?.[0]?.defaultCsrId, '_id', officeDrop)
                    )
                  }
                }, [stages, officeDrop, isLead])

                useEffect(() => {
                  if (isLead) {
                    setClientAddress(
                      `${leadInfo?.street || ''}, ${leadInfo?.city || ''}, ${leadInfo?.state || ''} ${
                        leadInfo?.zip || ''
                      }, USA`
                    )
                  }
                }, [isLead])

                useEffect(() => {
                  if (values.leadSourceName !== '') {
                    const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                    setSelectedLeadSourceObject(result.leadSourceObject)
                  }
                }, [values.leadSourceName])

                useEffect(() => {
                  setFieldValue('referredBy', selectedReferrer?.name)
                  setLocalReferrerId(selectedReferrer?.id!)
                }, [selectedReferrer?.id])

                useEffect(() => {
                  if (Object?.entries?.(clientAutoFill || {})?.length) {
                    // const sourceName = getLeadSrcDropdownName(
                    //   clientAutoFill?.sCampaignId || clientAutoFill?.sLeadSourceId,
                    //   leadSrcData
                    // )?.sourceName

                    setFieldValue('client', clientAutoFill?.clientName || '')
                    setFieldValue('street', clientAutoFill?.sStreet)
                    setFieldValue('city', clientAutoFill?.sCity)
                    setFieldValue('state', clientAutoFill?.sState)
                    setFieldValue('zip', clientAutoFill?.sZip)
                    // setFieldValue('leadSourceName', sourceName)
                    // setFieldValue('referredBy', getNameFrom_Id(clientAutoFill?.referredBy, refererres))
                    if (
                      clientAutoFill?.sState &&
                      clientAutoFill?.sCity &&
                      clientAutoFill?.sZip &&
                      clientAutoFill?.sState
                    ) {
                      setClientAddress(
                        `${clientAutoFill?.sStreet || ''}, ${clientAutoFill?.sCity || ''}, ${
                          clientAutoFill?.sState || ''
                        } ${clientAutoFill?.sZip || ''}, USA`
                      )
                    }
                  }
                }, [clientAutoFill])

                useEffect(() => {
                  if (clientAddress !== '') {
                    getDistanceAndDuration(companySettingForAll?.address, clientAddress)
                      .then(({ distance, duration }: any) => {
                        const distanceInfo = Math.ceil(Number(distance / 1609.34))
                        const durationInfo = Math.round(duration / 60) ?? 0
                        setFieldValue('distance', distanceInfo)
                        setFieldValue('duration', durationInfo)
                      })
                      .catch((error) => {
                        console.error(error)
                      })
                  } else {
                    setFieldValue('distance', 0)
                    setFieldValue('duration', 0)
                  }
                  // }
                }, [clientAddress])

                useEffect(() => {
                  setClientName?.(values.client)
                  setSearchValue(values.client)
                }, [values.client])

                useEffect(() => {
                  if (duration > 0) {
                    setFieldValue('duration', duration)
                  }
                }, [duration])

                useEffect(() => {
                  if (values.oppType) {
                    setWorkType(values.oppType)
                  }
                }, [values.oppType])

                useEffect(() => {
                  if (workType) {
                    setFieldValue('oppType', workType)
                  }
                }, [workType])

                useEffect(() => {
                  if (selectedType) {
                    const result = projectTypes?.find((v) => v.name === selectedType)?.questions || []
                    setFieldValue('questions', result)
                  }
                }, [selectedType, projectTypes])

                useEffect(() => {
                  if (values.contactId !== '') {
                    fetchClientById(values.contactId)
                  }
                }, [values.contactId])

                useEffect(() => {
                  if (clientData?._id) {
                    setFieldValue('client', clientData?.fullName)
                    setFieldValue('contactId', clientData?._id)
                  }
                }, [clientData])

                useEffect(() => {
                  if (activeLead) {
                    setFieldValue('leadSourceName', activeLead?.campaignName || activeLead?.leadSourceName || '')
                    setFieldValue('newLeadDate', activeLead?.newLeadDate || '')
                  } else if (openOppLeads?.leads) {
                    setFieldValue(
                      'leadSourceName',
                      openOppLeads?.leads?.campaignName || openOppLeads?.leads?.leadSourceName || ''
                    )
                    setFieldValue('newLeadDate', openOppLeads?.leads?.newLeadDate || '')
                    // setFieldValue('oppType', openOppLeads?.leads?.workType || '')
                  }
                }, [openOppLeads])

                return (
                  <Styled.NewLeadContainer>
                    <Form className="form">
                      <SharedStyled.Content
                        overflow={'unset'}
                        disableBoxShadow={true}
                        noPadding={true}
                        gap="8px"
                        width="100%"
                      >
                        <SharedDateAndTime
                          value={values.oppDate}
                          labelName={
                            isLead ? 'Date/Time converted to Opportunity *' : 'Date/Time Opportunity came in *'
                          }
                          stateName="oppDate"
                          setFieldValue={setFieldValue}
                          error={touched.oppDate && errors.oppDate ? true : false}
                        />

                        <>
                          <SharedStyled.Text
                            fontSize="16px"
                            fontWeight="700"
                            textAlign="left"
                            margin="8px auto -10px 0"
                          >
                            Project Info
                          </SharedStyled.Text>
                          <CustomSelect
                            labelName="Type of work requested*"
                            stateName="oppType"
                            error={!workType && touched.oppType && errors.oppType ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.oppType || workType}
                            dropDownData={projectTypesDrop.map(({ name }: { name: string }) => name)}
                            setValue={setSelectedType}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedStyled.FlexCol width="96%" margin="0 0 0 auto">
                            {values?.questions?.map((question, index) => {
                              const stateName = question?.replace(/\s+/g, '-').replace(/\?/g, '') // Removes spaces and question marks to create a valid name
                              return (
                                <InputWithValidation
                                  key={index}
                                  labelName={question}
                                  stateName={stateName}
                                  error={false}
                                />
                              )
                            })}

                            <Styled.TextArea
                              component="textarea"
                              placeholder="Comments/Notes"
                              as={Field}
                              name="notes"
                              marginTop="8px"
                              height="52px"
                              stateName="notes"
                              labelName="Opportunity Notes"
                              error={touched.notes && errors.notes ? true : false}
                            />
                          </SharedStyled.FlexCol>
                        </>

                        <SharedStyled.Text fontSize="16px" fontWeight="700" textAlign="left" margin="8px auto 0px 0">
                          Contact Info
                        </SharedStyled.Text>
                        <SharedStyled.Text fontSize="14px" fontWeight="400" textAlign="left" margin="0 auto 0px 0">
                          {activeLead ? (
                            <>
                              <br />
                              <input type="checkbox" onClick={() => setDisableContact((prev) => !prev)} /> This
                              Opportunity is for a different Contact
                            </>
                          ) : null}
                        </SharedStyled.Text>
                        {console.log({ activeLead })}
                        <>
                          {/* <AutoCompleteWithId
                            labelName="Select a contact *"
                            stateName="client"
                            error={touched.client && errors.client ? true : false}
                            setFieldValue={setFieldValue}
                            options={clientDropdown.map((client) => ({
                              label: client.name.trim(),
                              value: client._id,
                              ...client,
                            }))}
                            value={values.client || ''}
                            showAddOption
                            addNewText="Add contact"
                            autoFillData={clientDropdown}
                            onAddClick={() => {
                              setShowAddNewClientModal?.(true)
                            }}
                            disabled={disableContact}
                            className="clientHeight"
                            setClientAddress={setClientAddress}
                            apiSearch={true}
                            searchLoader={clientloader}
                          /> */}

                          <AutoComplete
                            labelName="Select a contact *"
                            stateName="client"
                            error={touched.client && errors.client ? true : false}
                            setFieldValue={setFieldValue}
                            options={getKeysFromObjects(clientDropdown, 'name').map((client) => client.trim())}
                            value={values.client || ''}
                            showAddOption
                            addNewText="Add contact"
                            autoFillData={clientDropdown}
                            onAddClick={() => {
                              setShowAddNewClientModal?.(true)
                            }}
                            disabled={disableContact}
                            className="clientHeight"
                            refererres={refererres}
                            leadSrcData={leadSrcData}
                            setClientAddress={setClientAddress}
                            apiSearch={true}
                            searchLoader={clientloader}
                          />
                        </>
                        {/* )} */}

                        {clientLoading ? (
                          <SharedStyled.FlexCol gap="5px">
                            <SLoader height={15} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                          </SharedStyled.FlexCol>
                        ) : hasValues(clientData) ? (
                          <SharedStyled.FlexCol gap="10px">
                            <SharedStyled.FlexRow justifyContent="space-between">
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {formatPhoneNumber(clientData.phone, '') || '--'}
                                </SharedStyled.Text>
                                <br /> <SharedStyled.Text fontSize="14px">{clientData.email || '--'}</SharedStyled.Text>
                              </div>
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {clientData.street !== '' ? clientData.street : ''}
                                  <br />
                                  {/* <b>City : </b> */}
                                  {clientData.city},&nbsp;
                                  {/* <b>State : </b> */}
                                  {clientData.state},&nbsp;
                                  {/* <b>Zip : </b> */}
                                  {clientData.zip}
                                </SharedStyled.Text>
                              </div>
                              <div>
                                <Button type="button" onClick={() => setShowEditClientModal?.(true)}>
                                  Edit
                                </Button>
                              </div>
                            </SharedStyled.FlexRow>

                            {linkedContacts?.length > 0 ? (
                              <>
                                <SharedStyled.FlexCol>
                                  {linkedContacts?.map(
                                    (
                                      linkedContact: {
                                        id: {
                                          fullName: string
                                          phone: string
                                          email: string
                                        }
                                      },
                                      index: number
                                    ) => (
                                      <SharedStyled.FlexRow
                                        width="95%"
                                        margin="0 0 0 auto"
                                        justifyContent="space-between"
                                      >
                                        <div>
                                          <SharedStyled.Text fontSize="12px">
                                            {index + 1}. {linkedContact?.id?.fullName || ''}
                                          </SharedStyled.Text>
                                        </div>
                                        <div>
                                          <SharedStyled.Text fontSize="12px">
                                            {formatPhoneNumber(linkedContact?.id?.phone, '')}
                                          </SharedStyled.Text>
                                        </div>
                                        <div>
                                          <SharedStyled.Text fontSize="12px">
                                            {linkedContact?.id?.email}
                                          </SharedStyled.Text>
                                        </div>
                                      </SharedStyled.FlexRow>
                                    )
                                  )}
                                </SharedStyled.FlexCol>
                              </>
                            ) : (
                              <></>
                            )}
                          </SharedStyled.FlexCol>
                        ) : (
                          <></>
                        )}

                        <Styled.AddressContainer>
                          {
                            <h2 className="sub-heading">
                              Address:{' '}
                              <SharedStyled.BlueEdit
                                onClick={() => {
                                  setToggleGoogleAddressInput(true)
                                  setAddressInputType('google')
                                }}
                              >
                                {toggleGoogleAddressInput ? '' : 'Edit'}
                              </SharedStyled.BlueEdit>
                            </h2>
                          }

                          {toggleGoogleAddressInput ? (
                            <Styled.GoogleSearchBox>
                              {addressInputType === 'custom' ? (
                                <Styled.AddressCont gap="12px" id="custom">
                                  <SharedStyled.FlexCol gap="4px">
                                    <SharedStyled.FlexRow className="input">
                                      <div title="Street" id="street">
                                        <InputWithValidation
                                          labelName="Street"
                                          stateName="street"
                                          error={touched.street && errors.street ? true : false}
                                          twoInput={true}
                                        />
                                      </div>
                                    </SharedStyled.FlexRow>

                                    <SharedStyled.FlexRow className="input" justifyContent="space-between">
                                      <div id="city">
                                        <InputWithValidation
                                          labelName="City"
                                          stateName="city"
                                          error={touched.city && errors.city ? true : false}
                                          twoInput={true}
                                        />
                                      </div>

                                      <div id="state">
                                        <CustomSelect
                                          dropDownData={companySettingForAll?.workingStates || []}
                                          setValue={() => {}}
                                          stateName="state"
                                          value={values.state}
                                          // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                                          setFieldValue={setFieldValue}
                                          labelName="State"
                                          innerHeight="52px"
                                          margin="10px 0 0 0"
                                        />
                                      </div>

                                      <div id="zip">
                                        <InputWithValidation
                                          labelName="Zip"
                                          stateName="zip"
                                          error={touched.zip && errors.zip ? true : false}
                                          twoInput={true}
                                        />
                                      </div>
                                    </SharedStyled.FlexRow>

                                    <SharedStyled.FlexRow
                                      style={{ width: '100%' }}
                                      margin="4px 0 0 0"
                                      flexWrap="wrap"
                                      justifyContent="space-between"
                                    >
                                      <SharedStyled.FlexRow width="max-content">
                                        <Button
                                          // className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Save
                                        </Button>
                                        <Button
                                          className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Cancel
                                        </Button>
                                      </SharedStyled.FlexRow>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setAddressInputType('google')
                                          setToggleGoogleAddressInput(true)
                                        }}
                                        className="gray"
                                        width="max-content"
                                      >
                                        Google
                                      </Button>
                                    </SharedStyled.FlexRow>
                                  </SharedStyled.FlexCol>
                                </Styled.AddressCont>
                              ) : (
                                <Styled.AddressCont gap="12px" className="google" id="google">
                                  <SharedStyled.FlexCol margin="10px 0 0 0" width="100%">
                                    <AutoCompleteAddress
                                      setFieldValue={setFieldValue}
                                      street={'street'}
                                      city={'city'}
                                      state={'state'}
                                      zip={'zip'}
                                      distance={'distance'}
                                      duration={'duration'}
                                      sourceAddress={companySettingForAll?.address}
                                      companyLatLong={companySettingForAll}
                                      setDuration={setDuration}
                                      setDistance={setDistance}
                                      noLoadScript={true}
                                    />
                                    <SharedStyled.FlexRow
                                      style={{ width: '100%' }}
                                      margin="4px 0 0 0"
                                      flexWrap="wrap"
                                      justifyContent="space-between"
                                    >
                                      <SharedStyled.FlexRow width="max-content">
                                        <Button
                                          // className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Save
                                        </Button>
                                        <Button
                                          className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                          }}
                                        >
                                          Cancel
                                        </Button>
                                      </SharedStyled.FlexRow>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setAddressInputType('custom')
                                          setToggleGoogleAddressInput(true)
                                        }}
                                        className="gray"
                                        width="max-content"
                                      >
                                        Custom
                                      </Button>
                                    </SharedStyled.FlexRow>
                                  </SharedStyled.FlexCol>
                                </Styled.AddressCont>
                              )}
                            </Styled.GoogleSearchBox>
                          ) : (
                            !addressLoading &&
                            (values.street !== '' ||
                              values.city !== '' ||
                              values.state !== '' ||
                              values.zip !== '') && (
                              <div style={{ width: '100%' }}>
                                <SharedStyled.Text fontWeight="400">
                                  <span style={{ fontFamily: Nue.regular }}>
                                    {values.street !== '' ? values.street : ''}
                                  </span>
                                </SharedStyled.Text>
                                <br />
                                <SharedStyled.Text fontWeight="400">
                                  {/* <b>City : </b> */}
                                  <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                                  {/* <b>State : </b> */}
                                  <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                                  {/* <b>Zip : </b> */}
                                  <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                                </SharedStyled.Text>
                                &emsp;
                              </div>
                            )
                          )}
                        </Styled.AddressContainer>

                        {values.street && values.city && values.state && values.zip && (
                          <SharedStyled.TwoInputDiv>
                            <InputWithValidation
                              labelName="Drive Time"
                              stateName="duration"
                              twoInput={true}
                              disabled={addressInputType !== 'custom'}
                              error={touched.duration && errors.duration ? true : false}
                            />
                            <InputWithValidation
                              labelName="Distance"
                              stateName="distance"
                              twoInput={true}
                              disabled={addressInputType !== 'custom'}
                              error={touched.distance && errors.distance ? true : false}
                            />
                          </SharedStyled.TwoInputDiv>
                        )}

                        {leadSrcData?.length ? (
                          <AutoCompleteIndentation
                            labelName="Lead Source*"
                            stateName={`leadSourceName`}
                            isLeadSource
                            dropdownHeight="300px"
                            error={touched.leadSourceName && errors.leadSourceName ? true : false}
                            borderRadius="0px"
                            setFieldValue={setFieldValue}
                            options={mergeSourceAndCampaignNames(leadSrcData)}
                            formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                            value={values.leadSourceName!}
                            setValueOnClick={(val: string) => {
                              setFieldValue('leadSourceName', val)
                            }}
                            className="material-autocomplete"
                            isIndentation={true}
                          />
                        ) : null}

                        {/* hardcoded code */}
                        {selectedLeadSourceObject?.code === 'referral' && (
                          <SharedStyled.FlexBox width="95%" justifyContent="end" margin="0 0 0 auto">
                            {/* <CustomSelect
                              labelName="Referrer"
                              stateName="referredBy"
                              error={touched.referredBy && errors.referredBy ? true : false}
                              setFieldValue={setFieldValue}
                              setValue={setReferrerValue}
                              value={values.referredBy}
                              dropDownData={refererres?.map((item: any) => item?.name || '')}
                              innerHeight="52px"
                              className="top"
                              maxWidth="95%"
                              showAddOption
                              addNewText="--Add New--"
                              onAddClick={() => {
                                setShowReferrerModal?.(true)
                              }}
                            /> */}

                            <SearchableDropdown
                              label="Referrer"
                              placeholder="Type to search"
                              searchFunction={(val) => {
                                return fetchSearchReferer(val, false)
                              }}
                              displayKey={'name'}
                              refererOptions={refererres?.slice(0, 20)}
                              onSelect={(item: any) => {
                                setFieldValue('referredBy', item?.name)
                                setLocalReferrerId(item?._id)
                              }}
                              selectedValue={values.referredBy}
                              handleBlur={() => {}}
                              resultExtractor={(res) => res?.data?.data?.referrers || []}
                              showAddOption
                              onAddClick={() => {
                                setShowReferrerModal?.(true)
                              }}
                              onTextChange={(text) => {
                                setLocalReferrerData({
                                  leadSource: values?.leadSourceName,
                                  name: text,
                                })
                              }}
                              showUnKnownOption
                              onUnKnownClick={() => {
                                setFieldValue('referredBy', 'Unknown')
                              }}
                            />
                          </SharedStyled.FlexBox>
                        )}

                        <CustomSelect
                          labelName="CSR Assigned*"
                          stateName="CSRAssigned"
                          error={touched.CSRAssigned && errors.CSRAssigned ? true : false}
                          setFieldValue={setFieldValue}
                          value={values.CSRAssigned}
                          dropDownData={getKeysFromObjects(officeDrop, 'name')}
                          setValue={() => {}}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        {/* TODO: Change later */}
                        <SharedStyled.FlexRow width="100%" alignItems="flex-start" gap="20px"></SharedStyled.FlexRow>

                        {leadSrcData?.length &&
                          isPayPerLead(getObjectFromKey(leadSrcData, 'name', values.leadSourceName)) && (
                            <InputWithValidation
                              labelName="Lead Cost"
                              stateName="leadCost"
                              error={touched.leadCost && errors.leadCost ? true : false}
                              twoInput={true}
                            />
                          )}

                        <div style={{ width: '100%' }}>
                          <SharedStyled.Text
                            fontSize="16px"
                            fontWeight="700"
                            textAlign="left"
                            margin="8px auto -10px 0"
                          >
                            Set Appointment
                          </SharedStyled.Text>

                          <CustomSelect
                            labelName="Sales person assigned *"
                            stateName="salesPerson"
                            error={touched.salesPerson && errors.salesPerson ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.salesPerson}
                            dropDownData={getKeysFromObjects(salesPersonDrop, 'name')}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedStyled.TwoInputDiv>
                            <SharedDateAndTime
                              value={values.needsAssessmentDate}
                              labelName={'Needs Assessment Date/Time'}
                              stateName="needsAssessmentDate"
                              setFieldValue={setFieldValue}
                              error={touched.needsAssessmentDate && errors.needsAssessmentDate ? true : false}
                            />
                          </SharedStyled.TwoInputDiv>
                        </div>

                        <SharedStyled.FlexBox width="100%" alignItems="center" gap="20px" wrap="wrap" marginTop="25px">
                          <Button
                            disabled={loading}
                            onClick={() => {
                              errors.client && isLead ? notify('Contact is Required', 'error') : null
                              errors.duration
                                ? notify('Duration is required. Please add a valid address.', 'error')
                                : null
                            }}
                            isLoading={loading}
                            type="submit"
                            className="fit"
                          >
                            Save Opportunity
                          </Button>
                          <Button type="button" className="fit delete" onClick={onClose}>
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.Content>

                      <CustomModal show={foundOppModal}>
                        <FoundOpportunity
                          onConfirm={() => {
                            setFoundOppModal(false)
                          }}
                          opportunities={openOppLeads?.opps || []}
                          contactName={values.client || ''}
                          oppFinalPayload={{
                            newLeadDate: values.oppDate || '',
                            oppType: selectedType || '',
                            client: values.client || '',
                            oppNotes:
                              `${
                                formatQuestionsData(values?.questions, values)
                                  ? `${formatQuestionsData(values?.questions, values)}\n`
                                  : ''
                              }Comments/Notes: ${values.notes ? `**${values.notes.trim()}**` : ''}` || '',
                            leadSourceName:
                              getLeadSrcDropdownId(values.leadSourceName, leadSrcData)?.leadSourceName || '',
                            campaignName: getLeadSrcDropdownId(values.leadSourceName, leadSrcData)?.campaignName || '',
                            csrName: values.CSRAssigned || '',
                            salesPersonName: values.salesPerson || '',
                            needsAssessmentDate: values.needsAssessmentDate || '',
                          }}
                          // contactName={contactDetails?.isBusiness ? contactDetails?.businessName : contactDetails?.fullName}
                        />
                      </CustomModal>
                    </Form>
                  </Styled.NewLeadContainer>
                )
              }}
            </Formik>
          </>
        </SharedStyled.SettingModalContentContainer>
      </ModalContainer>

      <CustomModal show={infoOpportunityModal}>
        <InfoOpportunityModal
          onUseExisting={() => {
            handleCreateOpportunity({
              newLeadDate: openOppLeads?.leads?.newLeadDate,
              leadSource: openOppLeads?.leads?.leadSourceName,
              campaignName: openOppLeads?.leads?.campaignName || '',
              campaignId: openOppLeads?.leads?.campaignId || undefined,
              leadSourceId: openOppLeads?.leads?.leadSourceId,
              csrId: openOppLeads?.leads?.csrId,
              ...(openOppLeads?.leads?.workType && { oppType: openOppLeads?.leads?.workType }),
              createLead: false,
              leadId: openOppLeads?.leads?._id,
            })
            setCreateNew(false)
          }}
          onCreateNew={async () => {
            setInfoOpportunityModal(false)
            setCreateNew(true)
            handleCreateOpportunity({ createLead: true, leadId: openOppLeads?.leads?._id })
          }}
          leadSource={getLeadSrcDropdownName(openOppLeads?.leads?.leadSourceId, leadSrcData)?.sourceName || ''}
          campaign={getLeadSrcDropdownName(openOppLeads?.leads?.campaignId, leadSrcData)?.sourceName || ''}
          daysAgo={daysSince(new Date(openOppLeads?.leads?.newLeadDate).getTime()) || '--'}
        />
      </CustomModal>
    </>
  )
}

export default AddOpportunityModal
