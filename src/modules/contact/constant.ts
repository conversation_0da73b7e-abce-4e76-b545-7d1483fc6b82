export const typeDropdown = ['Lead', 'Client', 'Vendor', 'Team Member', 'Partner', 'Competitor', 'Linked', 'Other']

export enum Types {
  Lead = 'lead',
  Prospect = 'prospect',
  Client = 'client',
  Vendor = 'vendor',
  'Team Member' = 'teamMember',
  Partner = 'partner',
  Competitor = 'competitor',
  Linked = 'linked',
  Other = 'other',
  Spam = 'spam',
  Referrer = 'referrer',
}

export const TypesDropdown = [
  { name: 'Lead', _id: 'lead' },
  { name: 'Prospect', _id: 'prospect' },
  { name: 'Client', _id: 'client' },
  { name: 'Vendor', _id: 'vendor' },
  { name: 'Team Member', _id: 'teamMember' },
  { name: 'Partner', _id: 'partner' },
  { name: 'Competitor', _id: 'competitor' },
  { name: 'Linked', _id: 'linked' },
  { name: 'Other', _id: 'other' },
  { name: 'Spam', _id: 'spam' },
  { name: 'Referrer', _id: 'referrer' },
]

export const commentColorType: Record<string, string> = {
  '01': '#E63946',
  '02': '#F1A208',
  '03': '#43AA8B',
  '04': '#577590',
  '05': '#FF6B6B',
  '06': '#4D908E',
  '07': '#A29BFE',
  '08': '#6A994E',
  '09': '#F77F00',
  '10': '#8338EC',
}
export const ContactFields = [
  { name: 'Full Name', _id: 'fullName' },
  { name: 'Business Name', _id: 'businessName' },
  { name: 'Phone', _id: 'phone' },
  { name: 'Email', _id: 'email' },
]

export const trackingData = {
  adName: '',
  sessionSource: '',
  utmContent: '',
  utmCampaign: '',
  utmMedium: '',
  utmSource: '',
  referrer: '',
}
